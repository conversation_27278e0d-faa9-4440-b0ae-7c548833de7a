{".class": "MypyFile", "_fullname": "sre_constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ANY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.ANY", "name": "ANY", "type": "sre_constants._NamedIntConstant"}}, "ANY_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.ANY_ALL", "name": "ANY_ALL", "type": "sre_constants._NamedIntConstant"}}, "ASSERT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.ASSERT", "name": "ASSERT", "type": "sre_constants._NamedIntConstant"}}, "ASSERT_NOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.ASSERT_NOT", "name": "ASSERT_NOT", "type": "sre_constants._NamedIntConstant"}}, "AT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT", "name": "AT", "type": "sre_constants._NamedIntConstant"}}, "ATCODES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.ATCODES", "name": "ATCODES", "type": {".class": "Instance", "args": ["sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ATOMIC_GROUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.ATOMIC_GROUP", "name": "ATOMIC_GROUP", "type": "sre_constants._NamedIntConstant"}}, "AT_BEGINNING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_BEGINNING", "name": "AT_BEGINNING", "type": "sre_constants._NamedIntConstant"}}, "AT_BEGINNING_LINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_BEGINNING_LINE", "name": "AT_BEGINNING_LINE", "type": "sre_constants._NamedIntConstant"}}, "AT_BEGINNING_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_BEGINNING_STRING", "name": "AT_BEGINNING_STRING", "type": "sre_constants._NamedIntConstant"}}, "AT_BOUNDARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_BOUNDARY", "name": "AT_BOUNDARY", "type": "sre_constants._NamedIntConstant"}}, "AT_END": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_END", "name": "AT_END", "type": "sre_constants._NamedIntConstant"}}, "AT_END_LINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_END_LINE", "name": "AT_END_LINE", "type": "sre_constants._NamedIntConstant"}}, "AT_END_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_END_STRING", "name": "AT_END_STRING", "type": "sre_constants._NamedIntConstant"}}, "AT_LOCALE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_LOCALE", "name": "AT_LOCALE", "type": {".class": "Instance", "args": ["sre_constants._NamedIntConstant", "sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AT_LOC_BOUNDARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_LOC_BOUNDARY", "name": "AT_LOC_BOUNDARY", "type": "sre_constants._NamedIntConstant"}}, "AT_LOC_NON_BOUNDARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_LOC_NON_BOUNDARY", "name": "AT_LOC_NON_BOUNDARY", "type": "sre_constants._NamedIntConstant"}}, "AT_MULTILINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_MULTILINE", "name": "AT_MULTILINE", "type": {".class": "Instance", "args": ["sre_constants._NamedIntConstant", "sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AT_NON_BOUNDARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_NON_BOUNDARY", "name": "AT_NON_BOUNDARY", "type": "sre_constants._NamedIntConstant"}}, "AT_UNICODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_UNICODE", "name": "AT_UNICODE", "type": {".class": "Instance", "args": ["sre_constants._NamedIntConstant", "sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AT_UNI_BOUNDARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_UNI_BOUNDARY", "name": "AT_UNI_BOUNDARY", "type": "sre_constants._NamedIntConstant"}}, "AT_UNI_NON_BOUNDARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.AT_UNI_NON_BOUNDARY", "name": "AT_UNI_NON_BOUNDARY", "type": "sre_constants._NamedIntConstant"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BIGCHARSET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.BIGCHARSET", "name": "BIGCHARSET", "type": "sre_constants._NamedIntConstant"}}, "BRANCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.BRANCH", "name": "BRANCH", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY", "name": "CATEGORY", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_DIGIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_DIGIT", "name": "CATEGORY_DIGIT", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_LINEBREAK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_LINEBREAK", "name": "CATEGORY_LINEBREAK", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_LOC_NOT_WORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_LOC_NOT_WORD", "name": "CATEGORY_LOC_NOT_WORD", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_LOC_WORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_LOC_WORD", "name": "CATEGORY_LOC_WORD", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_NOT_DIGIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_NOT_DIGIT", "name": "CATEGORY_NOT_DIGIT", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_NOT_LINEBREAK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_NOT_LINEBREAK", "name": "CATEGORY_NOT_LINEBREAK", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_NOT_SPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_NOT_SPACE", "name": "CATEGORY_NOT_SPACE", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_NOT_WORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_NOT_WORD", "name": "CATEGORY_NOT_WORD", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_SPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_SPACE", "name": "CATEGORY_SPACE", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_UNI_DIGIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_UNI_DIGIT", "name": "CATEGORY_UNI_DIGIT", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_UNI_LINEBREAK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_UNI_LINEBREAK", "name": "CATEGORY_UNI_LINEBREAK", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_UNI_NOT_DIGIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_UNI_NOT_DIGIT", "name": "CATEGORY_UNI_NOT_DIGIT", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_UNI_NOT_LINEBREAK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_UNI_NOT_LINEBREAK", "name": "CATEGORY_UNI_NOT_LINEBREAK", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_UNI_NOT_SPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_UNI_NOT_SPACE", "name": "CATEGORY_UNI_NOT_SPACE", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_UNI_NOT_WORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_UNI_NOT_WORD", "name": "CATEGORY_UNI_NOT_WORD", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_UNI_SPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_UNI_SPACE", "name": "CATEGORY_UNI_SPACE", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_UNI_WORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_UNI_WORD", "name": "CATEGORY_UNI_WORD", "type": "sre_constants._NamedIntConstant"}}, "CATEGORY_WORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CATEGORY_WORD", "name": "CATEGORY_WORD", "type": "sre_constants._NamedIntConstant"}}, "CHARSET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CHARSET", "name": "CHARSET", "type": "sre_constants._NamedIntConstant"}}, "CHCODES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CHCODES", "name": "CHCODES", "type": {".class": "Instance", "args": ["sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "CH_LOCALE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CH_LOCALE", "name": "CH_LOCALE", "type": {".class": "Instance", "args": ["sre_constants._NamedIntConstant", "sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "CH_UNICODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.CH_UNICODE", "name": "CH_UNICODE", "type": {".class": "Instance", "args": ["sre_constants._NamedIntConstant", "sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "FAILURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.FAILURE", "name": "FAILURE", "type": "sre_constants._NamedIntConstant"}}, "GROUPREF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.GROUPREF", "name": "GROUPREF", "type": "sre_constants._NamedIntConstant"}}, "GROUPREF_EXISTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.GROUPREF_EXISTS", "name": "GROUPREF_EXISTS", "type": "sre_constants._NamedIntConstant"}}, "GROUPREF_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.GROUPREF_IGNORE", "name": "GROUPREF_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "GROUPREF_LOC_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.GROUPREF_LOC_IGNORE", "name": "GROUPREF_LOC_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "GROUPREF_UNI_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.GROUPREF_UNI_IGNORE", "name": "GROUPREF_UNI_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "IN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.IN", "name": "IN", "type": "sre_constants._NamedIntConstant"}}, "INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.INFO", "name": "INFO", "type": "sre_constants._NamedIntConstant"}}, "IN_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.IN_IGNORE", "name": "IN_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "IN_LOC_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.IN_LOC_IGNORE", "name": "IN_LOC_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "IN_UNI_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.IN_UNI_IGNORE", "name": "IN_UNI_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "JUMP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.JUMP", "name": "JUMP", "type": "sre_constants._NamedIntConstant"}}, "LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.LITERAL", "name": "LITERAL", "type": "sre_constants._NamedIntConstant"}}, "LITERAL_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.LITERAL_IGNORE", "name": "LITERAL_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "LITERAL_LOC_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.LITERAL_LOC_IGNORE", "name": "LITERAL_LOC_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "LITERAL_UNI_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.LITERAL_UNI_IGNORE", "name": "LITERAL_UNI_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "MAGIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.MAGIC", "name": "MAGIC", "type": "builtins.int"}}, "MARK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.MARK", "name": "MARK", "type": "sre_constants._NamedIntConstant"}}, "MAXGROUPS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.MAXGROUPS", "name": "MAXGROUPS", "type": "builtins.int"}}, "MAXREPEAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.MAXREPEAT", "name": "MAXREPEAT", "type": "sre_constants._NamedIntConstant"}}, "MAX_REPEAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.MAX_REPEAT", "name": "MAX_REPEAT", "type": "sre_constants._NamedIntConstant"}}, "MAX_UNTIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.MAX_UNTIL", "name": "MAX_UNTIL", "type": "sre_constants._NamedIntConstant"}}, "MIN_REPEAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.MIN_REPEAT", "name": "MIN_REPEAT", "type": "sre_constants._NamedIntConstant"}}, "MIN_REPEAT_ONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.MIN_REPEAT_ONE", "name": "MIN_REPEAT_ONE", "type": "sre_constants._NamedIntConstant"}}, "MIN_UNTIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.MIN_UNTIL", "name": "MIN_UNTIL", "type": "sre_constants._NamedIntConstant"}}, "NEGATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.NEGATE", "name": "NEGATE", "type": "sre_constants._NamedIntConstant"}}, "NOT_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.NOT_LITERAL", "name": "NOT_LITERAL", "type": "sre_constants._NamedIntConstant"}}, "NOT_LITERAL_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.NOT_LITERAL_IGNORE", "name": "NOT_LITERAL_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "NOT_LITERAL_LOC_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.NOT_LITERAL_LOC_IGNORE", "name": "NOT_LITERAL_LOC_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "NOT_LITERAL_UNI_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.NOT_LITERAL_UNI_IGNORE", "name": "NOT_LITERAL_UNI_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "OPCODES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.OPCODES", "name": "OPCODES", "type": {".class": "Instance", "args": ["sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "OP_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.OP_IGNORE", "name": "OP_IGNORE", "type": {".class": "Instance", "args": ["sre_constants._NamedIntConstant", "sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "OP_LOCALE_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.OP_LOCALE_IGNORE", "name": "OP_LOCALE_IGNORE", "type": {".class": "Instance", "args": ["sre_constants._NamedIntConstant", "sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "OP_UNICODE_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.OP_UNICODE_IGNORE", "name": "OP_UNICODE_IGNORE", "type": {".class": "Instance", "args": ["sre_constants._NamedIntConstant", "sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "POSSESSIVE_REPEAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.POSSESSIVE_REPEAT", "name": "POSSESSIVE_REPEAT", "type": "sre_constants._NamedIntConstant"}}, "POSSESSIVE_REPEAT_ONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.POSSESSIVE_REPEAT_ONE", "name": "POSSESSIVE_REPEAT_ONE", "type": "sre_constants._NamedIntConstant"}}, "RANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.RANGE", "name": "RANGE", "type": "sre_constants._NamedIntConstant"}}, "RANGE_UNI_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.RANGE_UNI_IGNORE", "name": "RANGE_UNI_IGNORE", "type": "sre_constants._NamedIntConstant"}}, "REPEAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.REPEAT", "name": "REPEAT", "type": "sre_constants._NamedIntConstant"}}, "REPEAT_ONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.REPEAT_ONE", "name": "REPEAT_ONE", "type": "sre_constants._NamedIntConstant"}}, "SRE_FLAG_ASCII": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SRE_FLAG_ASCII", "name": "SRE_FLAG_ASCII", "type": "builtins.int"}}, "SRE_FLAG_DEBUG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SRE_FLAG_DEBUG", "name": "SRE_FLAG_DEBUG", "type": "builtins.int"}}, "SRE_FLAG_DOTALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SRE_FLAG_DOTALL", "name": "SRE_FLAG_DOTALL", "type": "builtins.int"}}, "SRE_FLAG_IGNORECASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SRE_FLAG_IGNORECASE", "name": "SRE_FLAG_IGNORECASE", "type": "builtins.int"}}, "SRE_FLAG_LOCALE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SRE_FLAG_LOCALE", "name": "SRE_FLAG_LOCALE", "type": "builtins.int"}}, "SRE_FLAG_MULTILINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SRE_FLAG_MULTILINE", "name": "SRE_FLAG_MULTILINE", "type": "builtins.int"}}, "SRE_FLAG_UNICODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SRE_FLAG_UNICODE", "name": "SRE_FLAG_UNICODE", "type": "builtins.int"}}, "SRE_FLAG_VERBOSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SRE_FLAG_VERBOSE", "name": "SRE_FLAG_VERBOSE", "type": "builtins.int"}}, "SRE_INFO_CHARSET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SRE_INFO_CHARSET", "name": "SRE_INFO_CHARSET", "type": "builtins.int"}}, "SRE_INFO_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SRE_INFO_LITERAL", "name": "SRE_INFO_LITERAL", "type": "builtins.int"}}, "SRE_INFO_PREFIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SRE_INFO_PREFIX", "name": "SRE_INFO_PREFIX", "type": "builtins.int"}}, "SUBPATTERN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SUBPATTERN", "name": "SUBPATTERN", "type": "sre_constants._NamedIntConstant"}}, "SUCCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.SUCCESS", "name": "SUCCESS", "type": "sre_constants._NamedIntConstant"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_NamedIntConstant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sre_constants._NamedIntConstant", "name": "_NamedIntConstant", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sre_constants._NamedIntConstant", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sre_constants", "mro": ["sre_constants._NamedIntConstant", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "value", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "sre_constants._NamedIntConstant.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "value", "name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sre_constants._NamedIntConstant.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sre_constants._NamedIntConstant", "values": [], "variance": 0}}, "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _NamedIntConstant", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sre_constants._NamedIntConstant.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sre_constants._NamedIntConstant", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sre_constants._NamedIntConstant.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sre_constants._NamedIntConstant", "values": [], "variance": 0}]}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_constants._NamedIntConstant.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sre_constants._NamedIntConstant.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sre_constants._NamedIntConstant", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_constants.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "error": {".class": "SymbolTableNode", "cross_ref": "re.error", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\sre_constants.pyi"}