"""
Email simulator program using classes, methods, and functions.

This program creates an email simulator with the following functionality:
- Email class with constructor and methods
- Functions to populate inbox, list emails, read emails, and view unread emails
- Menu-driven interface for user interaction
"""

# --- OOP Email Simulator --- #

# --- Email Class --- #
class Email:
    """Email class to represent an email object with sender, subject, content, and read status."""

    def __init__(self, email_address, subject_line, email_content):
        """
        Constructor to initialize an Email object.

        Args:
            email_address (str): The email address of the sender
            subject_line (str): The subject line of the email
            email_content (str): The contents of the email
        """
        self.email_address = email_address
        self.subject_line = subject_line
        self.email_content = email_content
        self.has_been_read = False  # Initialize to False (unread)

    def mark_as_read(self):
        """Method to mark the email as read by setting has_been_read to True."""
        self.has_been_read = True


# --- Lists --- #
# Initialize an empty list to store the email objects
inbox = []


# --- Functions --- #
def populate_inbox():
    """
    Function that creates sample email objects and adds them to the inbox list.
    This function is called at program startup to populate the inbox with 3 sample emails.
    """
    # Create 3 sample emails and add them to the inbox list
    email1 = Email("<EMAIL>", "Welcome to HyperionDev!",
                   "Welcome to our bootcamp! We're excited to have you on board.")
    email2 = Email("<EMAIL>", "Great work on the bootcamp!",
                   "You're making excellent progress. Keep up the great work!")
    email3 = Email("<EMAIL>", "Your next assignment",
                   "Please find attached your next assignment. Good luck!")

    inbox.extend([email1, email2, email3])


def list_emails():
    """
    Function that prints each email's subject line alongside its corresponding index number,
    regardless of whether the email has been read.
    """
    print("\nInbox:")
    for i, email in enumerate(inbox):
        print(f"{i} {email.subject_line}")


def read_email(index):
    """
    Function that displays the email details for the selected email and marks it as read.

    Args:
        index (int): The index of the email to read from the inbox list
    """
    if 0 <= index < len(inbox):
        email = inbox[index]
        print(f"\nEmail from: {email.email_address}")
        print(f"Subject: {email.subject_line}")
        print(f"Content: {email.email_content}")
        email.mark_as_read()
        print("\nEmail marked as read.")
    else:
        print("Invalid email index.")


def view_unread_emails():
    """
    Function that displays all unread email subject lines along with their corresponding index numbers.
    The list updates as emails are read.
    """
    print("\nUnread emails:")
    unread_found = False
    for i, email in enumerate(inbox):
        if not email.has_been_read:
            print(f"{i} {email.subject_line}")
            unread_found = True

    if not unread_found:
        print("No unread emails.")


# --- Email Program --- #

# Call the function to populate the inbox for further use in your program
populate_inbox()

# Display the menu options for each iteration of the loop
while True:
    user_choice = int(
        input(
            """\nWould you like to:
    1. Read an email
    2. View unread emails
    3. Quit application

    Enter selection: """
        )
    )

    if user_choice == 1:
        # Logic to read an email
        list_emails()
        try:
            email_index = int(input("\nEnter the index of the email you want to read: "))
            read_email(email_index)
        except ValueError:
            print("Please enter a valid number.")
        except IndexError:
            print("Invalid email index.")

    elif user_choice == 2:
        # Logic to view unread emails
        view_unread_emails()

    elif user_choice == 3:
        # Logic to quit application
        print("Goodbye!")
        break

    else:
        print("Oops - incorrect input.")
