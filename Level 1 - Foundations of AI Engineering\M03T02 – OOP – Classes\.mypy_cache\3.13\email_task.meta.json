{"data_mtime": 1757550078, "dep_lines": [1, 1, 1, 1, 1, 1], "dep_prios": [5, 30, 30, 30, 30, 30], "dependencies": ["builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "a6b4cbdd4e3d00375793ac893da87e245682457f", "id": "email_task", "ignore_all": false, "interface_hash": "151a161a809a10c54187045f20e9584f3ca8cb2f", "mtime": 1757550078, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\dev\\GN25060018715\\Level 1 - Foundations of AI Engineering\\M03T02 – OOP – Classes\\Code Files\\email_task.py", "plugin_data": null, "size": 3182, "suppressed": [], "version_id": "1.15.0"}