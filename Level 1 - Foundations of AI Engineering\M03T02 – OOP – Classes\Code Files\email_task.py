"""
Starting template for creating an email simulator program using
classes, methods, and functions.

This template provides a foundational structure to develop your own
email simulator. It includes placeholder functions and conditional statements
with 'pass' statements to prevent crashes due to missing logic.
Replace these 'pass' statements with your implementation once you've added
the required functionality to each conditional statement and function.

Note: Throughout the code, update comments to reflect the changes and logic
you implement for each function and method.
"""

# --- OOP Email Simulator --- #

# --- Email Class --- #
# Create the class, constructor and methods to create a new Email object.

# Initialise the instance variables for each email.

# Create the 'mark_as_read()' method to change the 'has_been_read'
# instance variable for a specific object from False to True.


# --- Functions --- #
# Build out the required functions for your program.

class Email():
    def __init__(self, email_address, Subject_line, email_content):
        self.email_address = email_address
        self.Subject_line = Subject_line
        self.email_content = email_content
        self.has_been_read = False

    def mark_as_read(self):
        self.has_been_read = True

inbox = []

def populate_inbox():
    email1 = Email("<EMAIL>", "Samsung work.", "Same day differnt shit.")
    email2 = Email("<EMAIL>", "Repayment.", "I will pay you back in a week.")
    email3 = Email("<EMAIL>", "Thinking of you.", "I miss you.")
    inbox.extend([email1, email2, email3])

def list_emails():
    for i, email in enumerate(inbox):
        print(f"{i} {email.Subject_line}")

def read_email(index):
    if 0 <= index < len(inbox):
        email = inbox[index]
        print(f"email from: {email.email_address}")
        print(f"subject: {email.Subject_line}")
        print(f"conent: {email.email_content}")
        email.mark_as_read()
        print("The email has been read.")
    else:
        print("Wrong index")

def view_unread_emails():
    print("unread emails:")
    unread_found = False
    for i, email in enumerate(inbox):
        if not email.has_been_read:
            print(f"{i} {email.subject_line}")
            unread_found = True
    if not unread_found:
        print("No unread emails.")


# --- Lists --- #
# Initialise an empty list outside the class to store the email objects.

# --- Email Program --- #

# Call the function to populate the inbox for further use in your program.

# Fill in the logic for the various menu operations.

# Display the menu options for each iteration of the loop.
while True:
    user_choice = int(
        input(
            """\nWould you like to:
    1. Read an email
    2. View unread emails
    3. Quit application

    Enter selection: """
        )
    )

    if user_choice == 1:
        list_emails()
        try:
        pass

    elif user_choice == 2:
        # Add logic here to view unread emails
        pass

    elif user_choice == 3:
        # Add logic here to quit application.
        pass

    else:
        print("Oops - incorrect input.")
