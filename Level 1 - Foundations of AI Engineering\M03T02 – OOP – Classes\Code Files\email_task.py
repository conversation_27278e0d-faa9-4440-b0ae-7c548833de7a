class Email():
    def __init__(self, email_address, Subject_line, email_content):
        self.email_address = email_address
        self.Subject_line = Subject_line
        self.email_content = email_content
        self.has_been_read = False

    def mark_as_read(self):
        self.has_been_read = True

inbox = []

def populate_inbox():
    email1 = Email("<EMAIL>", "Samsung work.", "Same day differnt shit.")
    email2 = Email("<EMAIL>", "Repayment.", "I will pay you back in a week.")
    email3 = Email("<EMAIL>", "Thinking of you.", "I miss you.")
    inbox.extend([email1, email2, email3])

def list_emails():
    for i, email in enumerate(inbox):
        print(f"{i} {email.Subject_line}")

def read_email(index):
    if 0 <= index < len(inbox):
        email = inbox[index]
        print(f"email from: {email.email_address}")
        print(f"subject: {email.Subject_line}")
        print(f"conent: {email.email_content}")
        email.mark_as_read()
        print("The email has been read.")
    else:
        print("Wrong index")

def view_unread_emails():
    print("unread emails:")
    unread_found = False
    for i, email in enumerate(inbox):
        if not email.has_been_read:
            print(f"{i} {email.subject_line}")
            unread_found = True
    if not unread_found:
        print("No unread emails.")



while True:
    user_choice = int(
        input(
            """\nWould you like to:
    1. Read an email
    2. View unread emails
    3. Quit application

    Enter selection: """
        )
    )

    if user_choice == 1:
        list_emails()
        try:
            email_index = int(input("\nEnter the index of the email to read: "))
            read_email(email_index)
        except ValueError:
            print("please enter a valid number.")
        except IndexError:
            print("Invalid email index.")
    elif user_choice == 2:
        view_unread_emails()
    elif user_choice == 3:
        print("Bye Bye") 
        break   

    else:
        print("Oops - incorrect input.")
